import { RestClientV5 } from "bybit-api";
import { bybit } from "./env.ts";
import { TechnicalIndicators } from "./technical-indicators.ts";
import { MarketScanner } from "./market-scanner.ts";
import { StrategyEngine, TradingSignal } from "./strategy-engine.ts";
import { RateLimiter } from "./rate-limiter.ts";

const bybitClient = new RestClientV5({
  key: bybit.apiKey,
  secret: bybit.apiSecret,
  testnet: bybit.demo,
  demoTrading: bybit.demo,
  parseAPIRateLimits: true,
});

const rateLimiter = new RateLimiter({
  requestsPerSecond: 15,
  requestsPerMinute: 120,
  burstLimit: 8,
  useAPIRateLimit: true
});
const technicalIndicators = new TechnicalIndicators();
const marketScanner = new MarketScanner(bybitClient, rateLimiter);
const strategyEngine = new StrategyEngine(technicalIndicators);

const main = async () => {
  console.log("🚀 Starting Bybit Perpetual Futures Scanner...");

  try {
    // Get all available perpetual futures
    console.log("📊 Fetching available perpetual futures...");
    const symbols = await marketScanner.getAvailableSymbols();
    console.log(`Found ${symbols.length} perpetual futures symbols`);

    // Scan all symbols for trading opportunities
    console.log("🔍 Scanning for trading opportunities...");
    const signals = await marketScanner.scanAllSymbols(symbols, strategyEngine, {
      interval: '4h',
      limit: 50,
      minVolume: 5000000, // 5M USDT minimum volume
      maxSymbols: 30, // Limit to top 30 symbols
      onProgress: (completed, total, symbol) => {
        console.log(`📊 Progress: ${completed}/${total} - Analyzing ${symbol}`);
      }
    });

    // Display results
    console.log("\n📈 Trading Signals Generated:");
    console.log("=".repeat(50));

    const longSignals = signals.filter((s: TradingSignal) => s.signal === 'LONG');
    const shortSignals = signals.filter((s: TradingSignal) => s.signal === 'SHORT');

    console.log(`\n🟢 LONG Signals (${longSignals.length}):`);
    longSignals.forEach((signal: TradingSignal) => {
      console.log(`${signal.symbol}: ${signal.confidence}% confidence - ${signal.reason}`);
    });

    console.log(`\n🔴 SHORT Signals (${shortSignals.length}):`);
    shortSignals.forEach((signal: TradingSignal) => {
      console.log(`${signal.symbol}: ${signal.confidence}% confidence - ${signal.reason}`);
    });

    console.log(`\n⚪ No Signal: ${signals.filter((s: TradingSignal) => s.signal === 'HOLD').length} symbols`);

  } catch (error) {
    console.error("❌ Error in main execution:", error);
  }
};

if (import.meta.main) {
  main();
}
