import { RestClientV5 } from "bybit-api";
import { bybit } from "./env.ts";

const bybitClient = new RestClientV5({
  key: bybit.apiKey,
  secret: bybit.apiSecret,
  testnet: bybit.demo,
  demoTrading: bybit.demo,
  parseAPIRateLimits: true,
});

const main = async () => {
  const response = await bybitClient.getAccountInfo();
  const test = await bybitClient.getFundingRateHistory({
    symbol: "BTCUSDT",
    startTime: Date.now()-24*60*60*1000,
    endTime: Date.now(),
    category: "inverse"
  })
  console.log(response);
  console.log(test);
};

main();
