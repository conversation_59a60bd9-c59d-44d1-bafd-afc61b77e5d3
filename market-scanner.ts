import { RestClientV5 } from "bybit-api";
import { RateLimiter } from "./rate-limiter.ts";
import { StrategyEngine, TradingSignal } from "./strategy-engine.ts";
import { OHLCV } from "./technical-indicators.ts";

export interface SymbolInfo {
  symbol: string;
  baseCoin: string;
  quoteCoin: string;
  status: string;
  contractType: string;
}

export interface MarketData {
  symbol: string;
  price: number;
  volume24h: number;
  priceChange24h: number;
  priceChangePercent24h: number;
  klines: OHLCV[];
}

export class MarketScanner {
  constructor(
    private client: RestClientV5,
    private rateLimiter: RateLimiter
  ) {}

  /**
   * Get all available perpetual futures symbols
   */
  async getAvailableSymbols(): Promise<SymbolInfo[]> {
    try {
      const response = await this.rateLimiter.execute(async () => {
        return await this.client.getInstrumentsInfo({
          category: 'linear', // Perpetual futures
        });
      });

      if (!response.result?.list) {
        throw new Error('Failed to fetch instruments info');
      }

      return response.result.list
        .filter((instrument: any) => 
          instrument.contractType === 'LinearPerpetual' && 
          instrument.status === 'Trading'
        )
        .map((instrument: any) => ({
          symbol: instrument.symbol,
          baseCoin: instrument.baseCoin,
          quoteCoin: instrument.quoteCoin,
          status: instrument.status,
          contractType: instrument.contractType,
        }))
        .sort((a, b) => a.symbol.localeCompare(b.symbol));
    } catch (error) {
      console.error('Error fetching available symbols:', error);
      throw error;
    }
  }

  /**
   * Get market data for a specific symbol
   */
  async getMarketData(symbol: string, interval: string = '1h', limit: number = 100): Promise<MarketData | null> {
    try {
      // Get current ticker data
      const tickerResponse = await this.rateLimiter.execute(async () => {
        return await this.client.getTickers({
          category: 'linear',
          symbol: symbol,
        });
      });

      if (!tickerResponse.result?.list?.[0]) {
        console.warn(`No ticker data for ${symbol}`);
        return null;
      }

      const ticker = tickerResponse.result.list[0];

      // Get kline data
      const klineResponse = await this.rateLimiter.execute(async () => {
        return await this.client.getKline({
          category: 'linear',
          symbol: symbol,
          interval: interval,
          limit: limit,
        });
      });

      if (!klineResponse.result?.list) {
        console.warn(`No kline data for ${symbol}`);
        return null;
      }

      // Convert kline data to OHLCV format
      const klines: OHLCV[] = klineResponse.result.list
        .map((kline: any) => ({
          timestamp: parseInt(kline[0]),
          open: parseFloat(kline[1]),
          high: parseFloat(kline[2]),
          low: parseFloat(kline[3]),
          close: parseFloat(kline[4]),
          volume: parseFloat(kline[5]),
        }))
        .reverse(); // Bybit returns newest first, we want oldest first

      return {
        symbol: symbol,
        price: parseFloat(ticker.lastPrice),
        volume24h: parseFloat(ticker.volume24h),
        priceChange24h: parseFloat(ticker.price24hPcnt),
        priceChangePercent24h: parseFloat(ticker.price24hPcnt) * 100,
        klines: klines,
      };
    } catch (error) {
      console.error(`Error fetching market data for ${symbol}:`, error);
      return null;
    }
  }

  /**
   * Scan all symbols for trading opportunities
   */
  async scanAllSymbols(
    symbols: SymbolInfo[],
    strategyEngine: StrategyEngine,
    options: {
      interval?: string;
      limit?: number;
      minVolume?: number;
      maxSymbols?: number;
      onProgress?: (completed: number, total: number, symbol: string) => void;
    } = {}
  ): Promise<TradingSignal[]> {
    const {
      interval = '1h',
      limit = 100,
      minVolume = 1000000, // Minimum 24h volume in USDT
      maxSymbols = 50, // Limit to avoid overwhelming API
      onProgress
    } = options;

    // Filter symbols by volume and limit count
    console.log(`📊 Filtering symbols (min volume: ${minVolume} USDT)...`);
    
    // Get top symbols by volume first
    const topSymbols = symbols.slice(0, Math.min(maxSymbols * 2, symbols.length));
    
    const signals: TradingSignal[] = [];
    let processed = 0;

    // Process symbols in batches to respect rate limits
    const batchSize = 5;
    for (let i = 0; i < topSymbols.length; i += batchSize) {
      const batch = topSymbols.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (symbolInfo) => {
        try {
          const marketData = await this.getMarketData(symbolInfo.symbol, interval, limit);
          
          if (!marketData) {
            return null;
          }

          // Filter by volume
          if (marketData.volume24h < minVolume) {
            return null;
          }

          // Generate trading signal
          const signal = strategyEngine.analyzeSymbol(marketData);
          
          processed++;
          if (onProgress) {
            onProgress(processed, Math.min(maxSymbols, topSymbols.length), symbolInfo.symbol);
          }

          return signal;
        } catch (error) {
          console.error(`Error processing ${symbolInfo.symbol}:`, error);
          processed++;
          if (onProgress) {
            onProgress(processed, Math.min(maxSymbols, topSymbols.length), symbolInfo.symbol);
          }
          return null;
        }
      });

      const batchResults = await Promise.all(batchPromises);
      const validSignals = batchResults.filter((signal): signal is TradingSignal => signal !== null);
      signals.push(...validSignals);

      // Stop if we have enough signals or reached max symbols
      if (signals.length >= maxSymbols || processed >= maxSymbols) {
        break;
      }

      // Small delay between batches
      if (i + batchSize < topSymbols.length) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    // Sort signals by confidence
    return signals.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Get top symbols by 24h volume
   */
  async getTopSymbolsByVolume(limit: number = 20): Promise<SymbolInfo[]> {
    try {
      const symbols = await this.getAvailableSymbols();
      
      // Get ticker data for all symbols to sort by volume
      console.log('📊 Fetching volume data for ranking...');
      
      const volumeData: Array<{ symbol: string; volume: number }> = [];
      
      // Process in smaller batches for volume data
      const batchSize = 10;
      for (let i = 0; i < Math.min(symbols.length, 100); i += batchSize) {
        const batch = symbols.slice(i, i + batchSize);
        
        const batchPromises = batch.map(async (symbolInfo) => {
          try {
            const response = await this.rateLimiter.execute(async () => {
              return await this.client.getTickers({
                category: 'linear',
                symbol: symbolInfo.symbol,
              });
            });
            
            if (response.result?.list?.[0]) {
              const volume = parseFloat(response.result.list[0].volume24h);
              return { symbol: symbolInfo.symbol, volume };
            }
            return null;
          } catch (error) {
            console.error(`Error getting volume for ${symbolInfo.symbol}:`, error);
            return null;
          }
        });
        
        const batchResults = await Promise.all(batchPromises);
        const validResults = batchResults.filter((result): result is { symbol: string; volume: number } => result !== null);
        volumeData.push(...validResults);
        
        // Small delay between batches
        if (i + batchSize < symbols.length) {
          await new Promise(resolve => setTimeout(resolve, 300));
        }
      }
      
      // Sort by volume and return top symbols
      const topSymbolNames = volumeData
        .sort((a, b) => b.volume - a.volume)
        .slice(0, limit)
        .map(item => item.symbol);
      
      return symbols.filter(symbol => topSymbolNames.includes(symbol.symbol));
    } catch (error) {
      console.error('Error getting top symbols by volume:', error);
      throw error;
    }
  }
}
